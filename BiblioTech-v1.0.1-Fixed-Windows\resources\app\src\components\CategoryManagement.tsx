import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { PlusIcon, EditIcon, TrashIcon, CheckIcon } from './Icons';
import type { Category } from '../types/library';

interface CategoryManagementProps {
  categories: Category[];
  onAddCategory: (name: string, description?: string) => Promise<boolean>;
  onUpdateCategory: (id: number, name: string, description?: string) => Promise<boolean>;
  onDeleteCategory: (id: number) => Promise<boolean>;
  onReorderCategories: (categories: Category[]) => Promise<boolean>;
  booksInCategory: (categoryName: string) => number;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory,
  onReorderCategories,
  booksInCategory
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });
  const [editCategory, setEditCategory] = useState({ name: '', description: '' });
  const [draggedItem, setDraggedItem] = useState<number | null>(null);
  const { logAction } = useAuth();

  const handleAddCategory = async () => {
    if (!newCategory.name.trim()) {
      alert('Le nom de la catégorie est requis');
      return;
    }

    if (categories.some(cat => cat.name.toLowerCase() === newCategory.name.toLowerCase())) {
      alert('Cette catégorie existe déjà');
      return;
    }

    const success = await onAddCategory(newCategory.name.trim(), newCategory.description.trim() || undefined);
    if (success) {
      setNewCategory({ name: '', description: '' });
      setIsAdding(false);
      logAction('CREATE', 'CATEGORY', undefined, `Catégorie créée: ${newCategory.name}`);
    } else {
      alert('Erreur lors de la création de la catégorie');
    }
  };

  const handleUpdateCategory = async (id: number) => {
    if (!editCategory.name.trim()) {
      alert('Le nom de la catégorie est requis');
      return;
    }

    const existingCategory = categories.find(cat => 
      cat.id !== id && cat.name.toLowerCase() === editCategory.name.toLowerCase()
    );
    
    if (existingCategory) {
      alert('Cette catégorie existe déjà');
      return;
    }

    const success = await onUpdateCategory(id, editCategory.name.trim(), editCategory.description.trim() || undefined);
    if (success) {
      setEditingId(null);
      setEditCategory({ name: '', description: '' });
      logAction('UPDATE', 'CATEGORY', id, `Catégorie modifiée: ${editCategory.name}`);
    } else {
      alert('Erreur lors de la modification de la catégorie');
    }
  };

  const handleDeleteCategory = async (id: number, name: string) => {
    const bookCount = booksInCategory(name);
    
    if (bookCount > 0) {
      const confirmed = window.confirm(
        `Cette catégorie contient ${bookCount} livre(s). Êtes-vous sûr de vouloir la supprimer ? Les livres seront déplacés vers "Non classé".`
      );
      if (!confirmed) return;
    } else {
      const confirmed = window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?');
      if (!confirmed) return;
    }

    const success = await onDeleteCategory(id);
    if (success) {
      logAction('DELETE', 'CATEGORY', id, `Catégorie supprimée: ${name}`);
    } else {
      alert('Erreur lors de la suppression de la catégorie');
    }
  };

  const startEdit = (category: Category) => {
    setEditingId(category.id);
    setEditCategory({ name: category.name, description: category.description || '' });
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditCategory({ name: '', description: '' });
  };

  const handleDragStart = (e: React.DragEvent, id: number) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, targetId: number) => {
    e.preventDefault();
    
    if (draggedItem === null || draggedItem === targetId) {
      setDraggedItem(null);
      return;
    }

    const draggedIndex = categories.findIndex(cat => cat.id === draggedItem);
    const targetIndex = categories.findIndex(cat => cat.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedItem(null);
      return;
    }

    const newCategories = [...categories];
    const [draggedCategory] = newCategories.splice(draggedIndex, 1);
    newCategories.splice(targetIndex, 0, draggedCategory);

    // Update order values
    const reorderedCategories = newCategories.map((cat, index) => ({
      ...cat,
      order: index
    }));

    const success = await onReorderCategories(reorderedCategories);
    if (success) {
      logAction('REORDER', 'CATEGORY', undefined, 'Catégories réorganisées');
    }

    setDraggedItem(null);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Gestion des catégories</h2>
            <p className="card-subtitle">Organisez et gérez les catégories de livres</p>
          </div>
          <button 
            onClick={() => setIsAdding(true)} 
            className="btn btn-primary"
            disabled={isAdding}
          >
            <PlusIcon size={16} />
            Nouvelle catégorie
          </button>
        </div>
      </div>

      <div className="card-content">
        {/* Add new category form */}
        {isAdding && (
          <div className="card mb-6" style={{ border: '2px solid var(--color-primary-200)' }}>
            <div className="card-content">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Nom de la catégorie *</label>
                  <input
                    type="text"
                    className="form-input"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Ex: Science-Fiction"
                    autoFocus
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Description (optionnel)</label>
                  <input
                    type="text"
                    className="form-input"
                    value={newCategory.description}
                    onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Description de la catégorie"
                  />
                </div>
              </div>
              <div className="flex gap-3 justify-end mt-4">
                <button 
                  onClick={() => {
                    setIsAdding(false);
                    setNewCategory({ name: '', description: '' });
                  }}
                  className="btn btn-secondary"
                >
                  Annuler
                </button>
                <button 
                  onClick={handleAddCategory}
                  className="btn btn-primary"
                  disabled={!newCategory.name.trim()}
                >
                  <CheckIcon size={16} />
                  Créer
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Categories list */}
        <div className="space-y-3">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`card ${draggedItem === category.id ? 'opacity-50' : ''}`}
              draggable
              onDragStart={(e) => handleDragStart(e, category.id)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, category.id)}
              style={{ cursor: 'move' }}
            >
              <div className="card-content">
                {editingId === category.id ? (
                  // Edit mode
                  <div>
                    <div className="form-row">
                      <div className="form-group">
                        <label className="form-label">Nom de la catégorie *</label>
                        <input
                          type="text"
                          className="form-input"
                          value={editCategory.name}
                          onChange={(e) => setEditCategory(prev => ({ ...prev, name: e.target.value }))}
                          autoFocus
                        />
                      </div>
                      <div className="form-group">
                        <label className="form-label">Description</label>
                        <input
                          type="text"
                          className="form-input"
                          value={editCategory.description}
                          onChange={(e) => setEditCategory(prev => ({ ...prev, description: e.target.value }))}
                        />
                      </div>
                    </div>
                    <div className="flex gap-3 justify-end mt-4">
                      <button onClick={cancelEdit} className="btn btn-secondary">
                        Annuler
                      </button>
                      <button 
                        onClick={() => handleUpdateCategory(category.id)}
                        className="btn btn-primary"
                        disabled={!editCategory.name.trim()}
                      >
                        <CheckIcon size={16} />
                        Sauvegarder
                      </button>
                    </div>
                  </div>
                ) : (
                  // View mode
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="text-lg font-semibold">{category.name}</div>
                        <div className="badge badge-neutral">
                          {booksInCategory(category.name)} livre(s)
                        </div>
                      </div>
                      {category.description && (
                        <div className="text-sm text-neutral mt-1">{category.description}</div>
                      )}
                      <div className="text-xs text-neutral mt-1">
                        Créée le {new Date(category.createdAt).toLocaleDateString('fr-FR')}
                      </div>
                    </div>
                    <div className="action-buttons">
                      <button
                        onClick={() => startEdit(category)}
                        className="icon-button"
                        title="Modifier"
                      >
                        <EditIcon size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category.id, category.name)}
                        className="icon-button danger"
                        title="Supprimer"
                      >
                        <TrashIcon size={16} />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {categories.length === 0 && (
          <div className="text-center py-8 text-neutral">
            <p>Aucune catégorie créée. Commencez par ajouter une nouvelle catégorie.</p>
          </div>
        )}

        <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
          <h4 className="font-semibold mb-2">💡 Conseils :</h4>
          <ul className="text-sm text-neutral space-y-1">
            <li>• Glissez-déposez les catégories pour les réorganiser</li>
            <li>• Les catégories avec des livres nécessitent une confirmation pour être supprimées</li>
            <li>• Les noms de catégories doivent être uniques</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CategoryManagement;
