// Simple script to create a basic icon for the application
const fs = require('fs');
const path = require('path');

// Create a simple base64 encoded ICO file (16x16 and 32x32)
// This is a minimal library icon representation
const icoData = Buffer.from([
  0x00, 0x00, 0x01, 0x00, 0x02, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x68, 0x04,
  0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x20, 0x20, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0xa8, 0x10,
  0x00, 0x00, 0x8e, 0x04, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00,
  0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
]);

// Create a more comprehensive icon data
const createIcon = () => {
  const iconPath = path.join(__dirname, 'public', 'assets', 'icon.ico');
  
  // Create a simple 16x16 blue book icon
  const iconBuffer = Buffer.alloc(1150); // Standard ICO file size for 16x16 + 32x32
  
  // ICO header
  iconBuffer.writeUInt16LE(0, 0);      // Reserved
  iconBuffer.writeUInt16LE(1, 2);      // Type (1 = ICO)
  iconBuffer.writeUInt16LE(2, 4);      // Number of images
  
  // Image 1 (16x16)
  iconBuffer.writeUInt8(16, 6);        // Width
  iconBuffer.writeUInt8(16, 7);        // Height
  iconBuffer.writeUInt8(0, 8);         // Color count
  iconBuffer.writeUInt8(0, 9);         // Reserved
  iconBuffer.writeUInt16LE(1, 10);     // Planes
  iconBuffer.writeUInt16LE(32, 12);    // Bits per pixel
  iconBuffer.writeUInt32LE(1024, 14);  // Size
  iconBuffer.writeUInt32LE(22, 18);    // Offset
  
  // Image 2 (32x32)
  iconBuffer.writeUInt8(32, 22);       // Width
  iconBuffer.writeUInt8(32, 23);       // Height
  iconBuffer.writeUInt8(0, 24);        // Color count
  iconBuffer.writeUInt8(0, 25);        // Reserved
  iconBuffer.writeUInt16LE(1, 26);     // Planes
  iconBuffer.writeUInt16LE(32, 28);    // Bits per pixel
  iconBuffer.writeUInt32LE(4096, 30);  // Size
  iconBuffer.writeUInt32LE(1046, 34);  // Offset
  
  // Fill with a simple blue book pattern
  for (let i = 38; i < iconBuffer.length; i += 4) {
    iconBuffer.writeUInt8(0x4A, i);     // Blue
    iconBuffer.writeUInt8(0x90, i + 1); // Green
    iconBuffer.writeUInt8(0xE2, i + 2); // Red
    iconBuffer.writeUInt8(0xFF, i + 3); // Alpha
  }
  
  fs.writeFileSync(iconPath, iconBuffer);
  console.log('Icon created at:', iconPath);
};

createIcon();
