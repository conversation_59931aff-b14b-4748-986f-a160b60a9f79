/* Library Management System - Component Styles */

/* Layout Components */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-neutral-50) 0%, var(--color-primary-50) 100%);
}

.app-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
}

.sidebar {
  background: var(--color-neutral-900);
  color: var(--color-neutral-100);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.sidebar-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-700);
}

.sidebar-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-400);
  margin-bottom: var(--space-2);
}

.sidebar-subtitle {
  font-size: var(--text-sm);
  color: var(--color-neutral-400);
}

.main-content {
  padding: var(--space-8);
  overflow-y: auto;
}

/* Navigation */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--color-neutral-300);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.nav-link:hover {
  background: var(--color-neutral-800);
  color: var(--color-neutral-100);
  text-decoration: none;
}

.nav-link.active {
  background: var(--color-primary-600);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--color-neutral-600);
}

/* Cards */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
  background: var(--color-neutral-50);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-1);
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
}

.card-content {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-4) var(--space-6);
  background: var(--color-neutral-50);
  border-top: 1px solid var(--color-neutral-200);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 1px solid transparent;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--color-primary-600);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-700);
  text-decoration: none;
  color: white;
}

.btn-secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border-color: var(--color-neutral-300);
}

.btn-secondary:hover {
  background: var(--color-neutral-200);
  text-decoration: none;
  color: var(--color-neutral-800);
}

.btn-success {
  background: var(--color-success-600);
  color: white;
}

.btn-success:hover {
  background: var(--color-success-700);
  text-decoration: none;
  color: white;
}

.btn-warning {
  background: var(--color-warning-600);
  color: white;
}

.btn-warning:hover {
  background: var(--color-warning-700);
  text-decoration: none;
  color: white;
}

.btn-error {
  background: var(--color-error-600);
  color: white;
}

.btn-error:hover {
  background: var(--color-error-700);
  text-decoration: none;
  color: white;
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-5);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
  margin-bottom: var(--space-2);
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: white;
}

.form-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.form-input:invalid {
  border-color: var(--color-error-500);
}

.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background: white;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.form-select:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--color-neutral-200);
}

/* Tables */
.table-container {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th {
  background: var(--color-neutral-50);
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--color-neutral-700);
  border-bottom: 1px solid var(--color-neutral-200);
  position: relative;
  cursor: pointer;
  user-select: none;
}

.table th:hover {
  background: var(--color-neutral-100);
}

.table th.sortable::after {
  content: '↕';
  position: absolute;
  right: var(--space-3);
  opacity: 0.5;
  font-size: var(--text-xs);
}

.table th.sort-asc::after {
  content: '↑';
  opacity: 1;
}

.table th.sort-desc::after {
  content: '↓';
  opacity: 1;
}

.table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--color-neutral-100);
  vertical-align: middle;
}

.table tr:hover {
  background: var(--color-neutral-50);
}

.table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-success {
  background: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-warning {
  background: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-error {
  background: var(--color-error-100);
  color: var(--color-error-700);
}

.badge-neutral {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
}

/* User Cards */
.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-6);
}

.user-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.user-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto var(--space-4);
  border: 3px solid var(--color-primary-100);
}

.user-name {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.user-info {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
}

/* Search and Filters */
.search-bar {
  position: relative;
  margin-bottom: var(--space-6);
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: white;
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--color-neutral-400);
}

.filter-bar {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
  white-space: nowrap;
}

.filter-select {
  min-width: 150px;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: white;
}

/* Category Pills */
.category-pills {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
  margin-bottom: var(--space-6);
}

.category-pill {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 2px solid var(--color-neutral-200);
  background: white;
  color: var(--color-neutral-700);
}

.category-pill:hover {
  border-color: var(--color-primary-300);
  background: var(--color-primary-50);
}

.category-pill.active {
  border-color: var(--color-primary-500);
  background: var(--color-primary-500);
  color: white;
}

/* Dashboard Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  padding: var(--space-6);
  text-align: center;
}

.stat-value {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-600);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-300);
  background: white;
  color: var(--color-neutral-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.icon-button:hover {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
  color: var(--color-neutral-700);
}

.icon-button.danger:hover {
  background: var(--color-error-50);
  border-color: var(--color-error-300);
  color: var(--color-error-600);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-bold); }
.font-semibold { font-weight: var(--font-semibold); }
.font-medium { font-weight: var(--font-medium); }

.text-primary { color: var(--color-primary-600); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }
.text-neutral { color: var(--color-neutral-600); }
.text-sm { font-size: var(--text-sm); }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.rounded-full { border-radius: 50%; }
.object-cover { object-fit: cover; }
.mx-auto { margin-left: auto; margin-right: auto; }
.opacity-50 { opacity: 0.5; }
.space-y-1 > * + * { margin-top: var(--space-1); }

/* Keyboard shortcut styling */
kbd {
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
}

/* Additional utility classes */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.max-h-32 {
  max-height: 8rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.space-y-3 > * + * {
  margin-top: var(--space-3);
}

.bg-neutral-50 {
  background-color: var(--color-neutral-50);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.border-t {
  border-top-width: 1px;
}

.border-neutral-700 {
  border-color: var(--color-neutral-700);
}

.bg-primary-500 {
  background-color: var(--color-primary-500);
}

.text-white {
  color: white;
}

.text-neutral-100 {
  color: var(--color-neutral-100);
}

.text-neutral-300 {
  color: var(--color-neutral-300);
}

.text-neutral-400 {
  color: var(--color-neutral-400);
}

.hover\:text-neutral-100:hover {
  color: var(--color-neutral-100);
}

.hover\:bg-neutral-800:hover {
  background-color: var(--color-neutral-800);
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.w-full {
  width: 100%;
}

.p-2 {
  padding: var(--space-2);
}

.pt-6 {
  padding-top: var(--space-6);
}

.mt-auto {
  margin-top: auto;
}

/* Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-800) 100%);
  padding: var(--space-4);
}

.login-card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-8);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.login-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-600);
  margin-bottom: var(--space-2);
}

.login-subtitle {
  color: var(--color-neutral-600);
  font-size: var(--text-base);
}

.login-form {
  margin-bottom: var(--space-6);
}

.login-button {
  width: 100%;
  padding: var(--space-4);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
}

.error-message {
  background: var(--color-error-50);
  color: var(--color-error-700);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-error-200);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
}

.login-demo-info {
  border-top: 1px solid var(--color-neutral-200);
  padding-top: var(--space-6);
  font-size: var(--text-sm);
}

.login-demo-info h4 {
  margin-bottom: var(--space-4);
  color: var(--color-neutral-700);
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.demo-account {
  background: var(--color-neutral-50);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
}

.demo-account code {
  background: var(--color-neutral-200);
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
}

/* Import Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
}

.modal-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.modal-subtitle {
  color: var(--color-neutral-600);
  font-size: var(--text-sm);
}

.modal-content {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-neutral-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* File Upload Styles */
.file-upload-area {
  border: 2px dashed var(--color-neutral-300);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--color-primary-400);
  background: var(--color-primary-50);
}

.file-upload-area.dragover {
  border-color: var(--color-primary-500);
  background: var(--color-primary-100);
}

.file-upload-icon {
  margin: 0 auto var(--space-4);
  color: var(--color-neutral-400);
}

.file-upload-text {
  color: var(--color-neutral-600);
  margin-bottom: var(--space-2);
}

.file-upload-hint {
  color: var(--color-neutral-500);
  font-size: var(--text-sm);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-neutral-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary-500);
  transition: width var(--transition-normal);
}

/* Import Preview Table */
.preview-table-container {
  max-height: 400px;
  overflow: auto;
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.preview-table th,
.preview-table td {
  padding: var(--space-2) var(--space-3);
  border-bottom: 1px solid var(--color-neutral-100);
  text-align: left;
}

.preview-table th {
  background: var(--color-neutral-50);
  font-weight: var(--font-semibold);
  position: sticky;
  top: 0;
}

.preview-table .error-row {
  background: var(--color-error-50);
}

.preview-table .error-cell {
  color: var(--color-error-600);
  font-weight: var(--font-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    display: none;
  }

  .main-content {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .user-grid {
    grid-template-columns: 1fr;
  }

  .modal {
    margin: var(--space-4);
    max-height: calc(100vh - 2rem);
  }

  .login-card {
    padding: var(--space-6);
  }
}
