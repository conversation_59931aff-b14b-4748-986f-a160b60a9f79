{"name": "bibliotech", "private": true, "version": "1.0.0", "description": "Système de gestion de bibliothèque moderne", "author": "BiblioTech Demo", "type": "module", "main": "public/electron.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron .\"", "build-electron": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist-win": "npm run build && electron-builder --win", "dist-portable": "npm run build && electron-builder --win portable"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.bibliotech.app", "productName": "BiblioTech", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "public/electron.cjs", "public/assets/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "BiblioTech", "uninstallDisplayName": "BiblioTech - Système de gestion de bibliothèque"}, "portable": {"artifactName": "BiblioTech-Portable-${version}.${ext}"}, "extraResources": [{"from": "public/assets", "to": "assets"}]}}