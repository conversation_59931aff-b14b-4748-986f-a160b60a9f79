// Composant pour afficher la liste des livres et leur statut d'emprunt
import React, { useState, useMemo } from 'react';
import { SearchIcon, EditIcon, TrashIcon, CheckIcon, XIcon, FilterIcon, DownloadIcon } from './Icons';

export type Livre = {
  id: number;
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  disponible: boolean;
  emprunteurId?: number;
  dateEmprunt?: string;
  dateRetour?: string;
};

interface LivreListProps {
  livres: Livre[];
  onSelectLivre?: (livre: Livre) => void;
  onDelete?: (id: number) => void;
  onToggleAvailability?: (id: number) => void;
  searchTerm?: string;
  onSearchChange?: (term: string) => void;
}

type SortField = 'titre' | 'auteur' | 'categorie' | 'genre' | 'disponible';
type SortDirection = 'asc' | 'desc';

const LivreList: React.FC<LivreListProps> = ({
  livres,
  onSelectLivre,
  onDelete,
  onToggleAvailability,
  searchTerm = '',
  onSearchChange
}) => {
  const [sortField, setSortField] = useState<SortField>('titre');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'available' | 'borrowed'>('all');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const filteredAndSortedLivres = useMemo(() => {
    let filtered = livres;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(livre =>
        livre.titre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.auteur.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.categorie.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.genre.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus === 'available') {
      filtered = filtered.filter(livre => livre.disponible);
    } else if (filterStatus === 'borrowed') {
      filtered = filtered.filter(livre => !livre.disponible);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string | boolean = a[sortField];
      let bValue: string | boolean = b[sortField];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [livres, searchTerm, filterStatus, sortField, sortDirection]);

  const handleExport = () => {
    const csvContent = [
      ['Titre', 'Auteur', 'Catégorie', 'Genre', 'Statut'],
      ...filteredAndSortedLivres.map(livre => [
        livre.titre,
        livre.auteur,
        livre.categorie,
        livre.genre,
        livre.disponible ? 'Disponible' : 'Emprunté'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'livres.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Liste des livres</h2>
            <p className="card-subtitle">{filteredAndSortedLivres.length} livre(s) trouvé(s)</p>
          </div>
          <button onClick={handleExport} className="btn btn-secondary">
            <DownloadIcon size={16} />
            Exporter CSV
          </button>
        </div>
      </div>

      <div className="card-content">
        {/* Search and Filters */}
        <div className="search-bar">
          <SearchIcon className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Rechercher par titre, auteur, catégorie ou genre..."
            value={searchTerm}
            onChange={(e) => onSearchChange?.(e.target.value)}
          />
        </div>

        <div className="filter-bar">
          <div className="filter-group">
            <FilterIcon size={16} />
            <span className="filter-label">Statut :</span>
            <select
              className="filter-select"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
            >
              <option value="all">Tous les livres</option>
              <option value="available">Disponibles</option>
              <option value="borrowed">Empruntés</option>
            </select>
          </div>
        </div>

        {/* Table */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th
                  className={`sortable ${sortField === 'titre' ? `sort-${sortDirection}` : ''}`}
                  onClick={() => handleSort('titre')}
                >
                  Titre
                </th>
                <th
                  className={`sortable ${sortField === 'auteur' ? `sort-${sortDirection}` : ''}`}
                  onClick={() => handleSort('auteur')}
                >
                  Auteur
                </th>
                <th
                  className={`sortable ${sortField === 'categorie' ? `sort-${sortDirection}` : ''}`}
                  onClick={() => handleSort('categorie')}
                >
                  Catégorie
                </th>
                <th
                  className={`sortable ${sortField === 'genre' ? `sort-${sortDirection}` : ''}`}
                  onClick={() => handleSort('genre')}
                >
                  Genre
                </th>
                <th
                  className={`sortable ${sortField === 'disponible' ? `sort-${sortDirection}` : ''}`}
                  onClick={() => handleSort('disponible')}
                >
                  Statut
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredAndSortedLivres.map((livre) => (
                <tr
                  key={livre.id}
                  style={{ cursor: onSelectLivre ? 'pointer' : 'default' }}
                  onClick={() => onSelectLivre && onSelectLivre(livre)}
                >
                  <td className="font-medium">{livre.titre}</td>
                  <td>{livre.auteur}</td>
                  <td>{livre.categorie}</td>
                  <td>{livre.genre}</td>
                  <td>
                    <span className={`badge ${livre.disponible ? 'badge-success' : 'badge-warning'}`}>
                      {livre.disponible ? 'Disponible' : 'Emprunté'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      {onToggleAvailability && (
                        <button
                          className="icon-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            onToggleAvailability(livre.id);
                          }}
                          title={livre.disponible ? 'Marquer comme emprunté' : 'Marquer comme disponible'}
                        >
                          {livre.disponible ? <XIcon size={16} /> : <CheckIcon size={16} />}
                        </button>
                      )}
                      <button
                        className="icon-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          // TODO: Implement edit functionality
                        }}
                        title="Modifier"
                      >
                        <EditIcon size={16} />
                      </button>
                      {onDelete && (
                        <button
                          className="icon-button danger"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (window.confirm('Êtes-vous sûr de vouloir supprimer ce livre ?')) {
                              onDelete(livre.id);
                            }
                          }}
                          title="Supprimer"
                        >
                          <TrashIcon size={16} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredAndSortedLivres.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <p>Aucun livre trouvé.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LivreList;
