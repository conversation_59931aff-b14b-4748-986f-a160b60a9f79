// Composant pour afficher les profils d'utilisateurs (emprunteurs)
import React, { useState } from 'react';
import { UsersIcon, SearchIcon, PlusIcon } from './Icons';

export type Utilisateur = {
  id: number;
  nom: string;
  photo: string; // URL de la photo de profil
};

interface Props {
  utilisateurs: Utilisateur[];
  onSelect?: (utilisateur: Utilisateur) => void;
}

const UtilisateurList: React.FC<Props> = ({ utilisateurs, onSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = utilisateurs.filter(user =>
    user.nom.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title flex items-center gap-2">
              <UsersIcon size={24} />
              Profils d'utilisateurs
            </h2>
            <p className="card-subtitle">{filteredUsers.length} utilisateur(s) enregistré(s)</p>
          </div>
          <button className="btn btn-primary">
            <PlusIcon size={16} />
            Nouvel utilisateur
          </button>
        </div>
      </div>

      <div className="card-content">
        {/* Search Bar */}
        <div className="search-bar">
          <SearchIcon className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Rechercher un utilisateur..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* User Grid */}
        <div className="user-grid">
          {filteredUsers.map(user => (
            <div
              key={user.id}
              className="user-card"
              onClick={() => onSelect && onSelect(user)}
            >
              <img
                src={user.photo}
                alt={user.nom}
                className="user-avatar"
                onError={(e) => {
                  // Fallback to a default avatar if image fails to load
                  (e.target as HTMLImageElement).src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.nom)}&background=0ea5e9&color=fff`;
                }}
              />
              <div className="user-name">{user.nom}</div>
              <div className="user-info">ID: {user.id}</div>
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-neutral">
            <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
            <p>Aucun utilisateur trouvé.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UtilisateurList;
