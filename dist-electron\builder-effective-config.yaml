directories:
  output: dist-electron
  buildResources: build
appId: com.bibliotech.app
productName: BiblioTech
copyright: Copyright © 2025 BiblioTech Team
files:
  - filter:
      - dist/**/*
      - public/electron.cjs
      - public/assets/**/*
      - public/usersimages/**/*
      - node_modules/**/*
      - '!node_modules/.cache'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
win:
  target:
    - target: portable
      arch:
        - x64
  icon: public/assets/icon.png
  requestedExecutionLevel: asInvoker
  verifyUpdateCodeSignature: false
  artifactName: BiblioTech-Setup-${version}-${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: false
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: BiblioTech
  uninstallDisplayName: BiblioTech - Système de gestion de bibliothèque
  installerIcon: public/assets/icon.png
  uninstallerIcon: public/assets/icon.png
  installerHeaderIcon: public/assets/icon.png
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: Office
portable:
  artifactName: BiblioTech-Portable-${version}-${arch}.${ext}
  requestExecutionLevel: user
extraResources:
  - from: public/assets
    to: assets
  - from: public/usersimages
    to: usersimages
compression: maximum
npmRebuild: false
nodeGypRebuild: false
electronVersion: 36.4.0
