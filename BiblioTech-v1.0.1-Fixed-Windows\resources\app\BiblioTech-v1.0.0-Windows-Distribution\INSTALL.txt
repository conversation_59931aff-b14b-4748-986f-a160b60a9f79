BiblioTech Library Management System v1.0.0
===========================================

QUICK START GUIDE
==================

OPTION 1: Run Portable Application (Recommended)
------------------------------------------------
1. Navigate to the "Application" folder
2. Double-click "Start-BiblioTech.bat" for guided startup
   OR
   Double-click "BiblioTech.exe" to run directly

OPTION 2: Copy to Desired Location
-----------------------------------
1. Copy the entire "Application" folder to your preferred location
   (e.g., C:\Programs\BiblioTech\)
2. Create a desktop shortcut to BiblioTech.exe if desired
3. Run the application

DEMO ACCOUNTS
=============
Super Administrator:
- Username: superadmin
- Password: admin123
- Full access to all features

Administrator:
- Username: admin1  
- Password: admin123
- Limited access (cannot manage other administrators)

SYSTEM REQUIREMENTS
===================
✓ Windows 10 (version 1903 or later) or Windows 11
✓ 64-bit architecture
✓ 4 GB RAM (recommended)
✓ 500 MB free disk space
✓ No additional software required
✓ No internet connection required

FEATURES OVERVIEW
=================
✓ Complete offline library management
✓ Book management with bulk CSV/Excel import
✓ Dynamic category management with drag-and-drop
✓ User management with role-based access control
✓ Loan tracking with due date monitoring
✓ Comprehensive audit logging
✓ Modern desktop interface with keyboard shortcuts
✓ Data export capabilities (CSV)
✓ Professional search and filtering

KEYBOARD SHORTCUTS
==================
Navigation:
- Ctrl+1: Dashboard
- Ctrl+2: Book Management  
- Ctrl+3: Categories
- Ctrl+4: Users
- Ctrl+5: Loan Tracking

Actions:
- Ctrl+N: New Book
- Ctrl+I: Bulk Import
- Ctrl+E: Export Data
- Ctrl+R: Refresh
- F11: Full Screen

TROUBLESHOOTING
===============
Application won't start:
- Ensure Windows Defender isn't blocking the executable
- Try running as Administrator
- Check that all files in the Application folder are present

Performance issues:
- Close other memory-intensive applications
- Ensure sufficient disk space
- Restart the application

Data not saving:
- This is a demonstration version with in-memory storage
- Data resets when the application is restarted
- For production use, database integration would be required

SECURITY NOTES
==============
- All data is stored locally (no cloud/internet required)
- Role-based access control with audit trails
- Session timeout after 30 minutes of inactivity
- Input validation and sanitization

DISTRIBUTION CONTENTS
====================
Application/          - Complete portable application
INSTALL.txt          - This installation guide

SUPPORT
=======
This is a demonstration version showcasing modern library management
system capabilities. The application is completely self-contained
and requires no external dependencies or internet connectivity.

© 2025 BiblioTech Demo - Modern Library Management System
