# BiblioTech Distribution Package Creator
# Creates a professional distribution package for stakeholders

Write-Host "BiblioTech Distribution Package Creator" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Continue"

# Navigate to project directory
$ProjectPath = "C:\My Software Projects\onlinelib"
Set-Location $ProjectPath

$DistributionName = "BiblioTech-v1.0.0-Windows-Distribution"
$DistributionPath = $DistributionName

Write-Host "Creating distribution package: $DistributionName" -ForegroundColor Yellow

# Clean up previous distribution
if (Test-Path $DistributionPath) {
    Remove-Item -Path $DistributionPath -Recurse -Force
}

# Create distribution structure
New-Item -ItemType Directory -Path $DistributionPath -Force | Out-Null
New-Item -ItemType Directory -Path "$DistributionPath\Application" -Force | Out-Null
New-Item -ItemType Directory -Path "$DistributionPath\Documentation" -Force | Out-Null
New-Item -ItemType Directory -Path "$DistributionPath\Source" -Force | Out-Null

Write-Host "Step 1: Copying application files..." -ForegroundColor Yellow

# Copy the portable application
if (Test-Path "BiblioTech-Portable") {
    Copy-Item -Path "BiblioTech-Portable\*" -Destination "$DistributionPath\Application" -Recurse
    Write-Host "✓ Portable application copied" -ForegroundColor Green
} else {
    Write-Host "✗ Portable application not found!" -ForegroundColor Red
    exit 1
}

Write-Host "Step 2: Copying documentation..." -ForegroundColor Yellow

# Copy documentation files
$docFiles = @(
    "USER_GUIDE.md",
    "BUILD_INSTRUCTIONS.md", 
    "DEPLOYMENT_GUIDE.md"
)

foreach ($file in $docFiles) {
    if (Test-Path $file) {
        Copy-Item -Path $file -Destination "$DistributionPath\Documentation\"
        Write-Host "✓ Copied $file" -ForegroundColor Green
    }
}

Write-Host "Step 3: Creating installation guide..." -ForegroundColor Yellow

# Create comprehensive installation guide
$installGuide = @"
BiblioTech Library Management System v1.0.0
===========================================

QUICK START GUIDE
==================

OPTION 1: Run Portable Application (Recommended)
------------------------------------------------
1. Navigate to the "Application" folder
2. Double-click "Start-BiblioTech.bat" for guided startup
   OR
   Double-click "BiblioTech.exe" to run directly

OPTION 2: Copy to Desired Location
-----------------------------------
1. Copy the entire "Application" folder to your preferred location
   (e.g., C:\Programs\BiblioTech\)
2. Create a desktop shortcut to BiblioTech.exe if desired
3. Run the application

DEMO ACCOUNTS
=============
Super Administrator:
- Username: superadmin
- Password: admin123
- Full access to all features

Administrator:
- Username: admin1  
- Password: admin123
- Limited access (cannot manage other administrators)

SYSTEM REQUIREMENTS
===================
✓ Windows 10 (version 1903 or later) or Windows 11
✓ 64-bit architecture
✓ 4 GB RAM (recommended)
✓ 500 MB free disk space
✓ No additional software required
✓ No internet connection required

FEATURES OVERVIEW
=================
✓ Complete offline library management
✓ Book management with bulk CSV/Excel import
✓ Dynamic category management with drag-and-drop
✓ User management with role-based access control
✓ Loan tracking with due date monitoring
✓ Comprehensive audit logging
✓ Modern desktop interface with keyboard shortcuts
✓ Data export capabilities (CSV)
✓ Professional search and filtering

KEYBOARD SHORTCUTS
==================
Navigation:
- Ctrl+1: Dashboard
- Ctrl+2: Book Management  
- Ctrl+3: Categories
- Ctrl+4: Users
- Ctrl+5: Loan Tracking

Actions:
- Ctrl+N: New Book
- Ctrl+I: Bulk Import
- Ctrl+E: Export Data
- Ctrl+R: Refresh
- F11: Full Screen

TROUBLESHOOTING
===============
Application won't start:
- Ensure Windows Defender isn't blocking the executable
- Try running as Administrator
- Check that all files in the Application folder are present

Performance issues:
- Close other memory-intensive applications
- Ensure sufficient disk space
- Restart the application

Data not saving:
- This is a demonstration version with in-memory storage
- Data resets when the application is restarted
- For production use, database integration would be required

SECURITY NOTES
==============
- All data is stored locally (no cloud/internet required)
- Role-based access control with audit trails
- Session timeout after 30 minutes of inactivity
- Input validation and sanitization

DISTRIBUTION CONTENTS
====================
Application/          - Complete portable application
Documentation/        - User guides and technical documentation
Source/              - Complete source code (if included)
INSTALL.txt          - This installation guide

SUPPORT
=======
This is a demonstration version showcasing modern library management
system capabilities. The application is completely self-contained
and requires no external dependencies or internet connectivity.

For technical details, see Documentation/BUILD_INSTRUCTIONS.md
For user instructions, see Documentation/USER_GUIDE.md

© 2025 BiblioTech Demo - Modern Library Management System
"@

Set-Content -Path "$DistributionPath\INSTALL.txt" -Value $installGuide

Write-Host "Step 4: Creating quick start batch file..." -ForegroundColor Yellow

# Create a quick start script for the distribution
$quickStart = @"
@echo off
title BiblioTech Library Management System
color 0A
cls

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    BiblioTech v1.0.0                        ║
echo  ║              Library Management System                       ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.
echo  Welcome to BiblioTech - Modern Library Management System
echo.
echo  DEMO ACCOUNTS:
echo  ┌─────────────────────────────────────────────────────────────┐
echo  │ Super Admin: superadmin / admin123                         │
echo  │ Administrator: admin1 / admin123                           │
echo  └─────────────────────────────────────────────────────────────┘
echo.
echo  FEATURES:
echo  • Complete offline library management
echo  • Book management with bulk import
echo  • Category and user management  
echo  • Loan tracking and audit logs
echo  • Modern desktop interface
echo.
echo  Press any key to start BiblioTech...
pause >nul

cd Application
start "" "BiblioTech.exe"
echo.
echo  BiblioTech is starting...
echo  You can close this window once the application opens.
echo.
timeout /t 3 >nul
"@

Set-Content -Path "$DistributionPath\Start-BiblioTech.bat" -Value $quickStart

Write-Host "Step 5: Calculating package information..." -ForegroundColor Yellow

# Calculate sizes and create summary
$appSize = (Get-ChildItem -Path "$DistributionPath\Application" -Recurse | Measure-Object -Property Length -Sum).Sum
$totalSize = (Get-ChildItem -Path $DistributionPath -Recurse | Measure-Object -Property Length -Sum).Sum

Write-Host "Step 6: Creating package summary..." -ForegroundColor Yellow

$summary = @"
BiblioTech Distribution Package Summary
======================================

Package: $DistributionName
Created: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

CONTENTS:
- Application Size: $([math]::Round($appSize / 1MB, 2)) MB
- Total Package Size: $([math]::Round($totalSize / 1MB, 2)) MB
- Files: $(Get-ChildItem -Path $DistributionPath -Recurse | Measure-Object | Select-Object -ExpandProperty Count)

COMPATIBILITY:
✓ Windows 10 (1903+) and Windows 11
✓ 64-bit architecture
✓ No dependencies required
✓ Completely portable
✓ Offline functionality

DISTRIBUTION READY:
The package is ready for distribution via:
- Email (if under size limits)
- USB drive
- Network share
- Cloud storage
- CD/DVD

VERIFICATION:
✓ Executable created successfully
✓ All dependencies included
✓ Documentation complete
✓ Installation guide provided
✓ Demo accounts configured

This package provides a complete, professional demonstration
of a modern library management system suitable for stakeholder
presentations and evaluation.
"@

Set-Content -Path "$DistributionPath\PACKAGE-INFO.txt" -Value $summary

Write-Host "Distribution package created successfully!" -ForegroundColor Green
Write-Host "Package location: $DistributionPath" -ForegroundColor Cyan
Write-Host "Application size: $([math]::Round($appSize / 1MB, 2)) MB" -ForegroundColor Cyan
Write-Host "Total package size: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Cyan

Write-Host "`nDISTRIBUTION READY!" -ForegroundColor Green -BackgroundColor Black
Write-Host "The stakeholder can now:" -ForegroundColor Yellow
Write-Host "1. Run 'Start-BiblioTech.bat' for guided startup" -ForegroundColor White
Write-Host "2. Or navigate to Application\ and run BiblioTech.exe directly" -ForegroundColor White
Write-Host "3. Read INSTALL.txt for complete instructions" -ForegroundColor White
