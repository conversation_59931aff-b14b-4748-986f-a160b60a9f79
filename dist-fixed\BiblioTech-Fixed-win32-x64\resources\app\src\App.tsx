import { useState, useEffect } from 'react';
import './App.css';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { getPermissions } from './types/auth';
import Login from './components/Login';
import LivreForm from './components/LivreForm';
import LivreList from './components/LivreList';
import Categories from './components/Categories';
import CategoryManagement from './components/CategoryManagement';
import UtilisateurList from './components/UtilisateurList';
import UserManagement from './components/UserManagement';
import SuiviPrets from './components/SuiviPrets';
import BulkImport from './components/BulkImport';
import AuditLogs from './components/AuditLogs';
import type { Utilisateur } from './components/UtilisateurList';
import type { Book, Category, BookImportData, ImportResult } from './types/library';
import { sanitizeBookData } from './types/library';
import {
  HomeIcon,
  BookIcon,
  CategoryIcon,
  UsersIcon,
  ClipboardIcon,
  UploadIcon,
  SettingsIcon,
  ShieldIcon,
  LogoutIcon
} from './components/Icons';

// Initial data
const INITIAL_CATEGORIES: Category[] = [
  { id: 1, name: 'Fiction', description: 'Romans et nouvelles', order: 0, createdAt: '2025-01-01T00:00:00Z', updatedAt: '2025-01-01T00:00:00Z' },
  { id: 2, name: 'Science', description: 'Ouvrages scientifiques', order: 1, createdAt: '2025-01-01T00:00:00Z', updatedAt: '2025-01-01T00:00:00Z' },
  { id: 3, name: 'Histoire', description: 'Livres d\'histoire', order: 2, createdAt: '2025-01-01T00:00:00Z', updatedAt: '2025-01-01T00:00:00Z' },
  { id: 4, name: 'Jeunesse', description: 'Littérature jeunesse', order: 3, createdAt: '2025-01-01T00:00:00Z', updatedAt: '2025-01-01T00:00:00Z' },
  { id: 5, name: 'Biographie', description: 'Biographies et mémoires', order: 4, createdAt: '2025-01-01T00:00:00Z', updatedAt: '2025-01-01T00:00:00Z' },
];

const UTILISATEURS: Utilisateur[] = [
  { id: 1, nom: 'Gabriel Ushindi', photo: '/usersimages/gabriel.jpg' },
  { id: 2, nom: 'Jocy Aza', photo: '/usersimages/jocyabusa.jpg' },
  { id: 3, nom: 'Sophie Nyota', photo: '/usersimages/grace_nyota.jpg' },
];

const LIVRES_MOCK: Book[] = [
  {
    id: 1,
    titre: 'Le Petit Prince',
    auteur: 'Antoine de Saint-Exupéry',
    categorie: 'Fiction',
    genre: 'Conte',
    isbn: '9782070408504',
    anneePublication: 1943,
    description: 'Un conte poétique et philosophique',
    disponible: false,
    emprunteurId: 1,
    dateEmprunt: '2025-06-01',
    dateRetour: '2025-06-15',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 2,
    titre: 'Une brève histoire du temps',
    auteur: 'Stephen Hawking',
    categorie: 'Science',
    genre: 'Physique',
    isbn: '9782070368228',
    anneePublication: 1988,
    disponible: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 3,
    titre: 'Les Misérables',
    auteur: 'Victor Hugo',
    categorie: 'Fiction',
    genre: 'Roman',
    anneePublication: 1862,
    disponible: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 4,
    titre: 'L\'Origine des espèces',
    auteur: 'Charles Darwin',
    categorie: 'Science',
    genre: 'Biologie',
    anneePublication: 1859,
    disponible: false,
    emprunteurId: 2,
    dateEmprunt: '2025-06-05',
    dateRetour: '2025-06-20',
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 5,
    titre: 'Histoire de France',
    auteur: 'Jules Michelet',
    categorie: 'Histoire',
    genre: 'Essai',
    anneePublication: 1833,
    disponible: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
];

// Main App Component with Authentication
const AppContent: React.FC = () => {
  const { user, logout, logAction } = useAuth();
  const [page, setPage] = useState('accueil');
  const [livres, setLivres] = useState<Book[]>(LIVRES_MOCK);
  const [categories, setCategories] = useState<Category[]>(INITIAL_CATEGORIES);
  const [selectedCategory, setSelectedCategory] = useState(INITIAL_CATEGORIES[0]?.name || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showBulkImport, setShowBulkImport] = useState(false);

  const permissions = user ? getPermissions(user.role) : null;

  // Navigation items based on permissions
  const getNavItems = () => {
    const baseNavs = [
      { key: 'accueil', label: 'Tableau de bord', icon: HomeIcon },
    ];

    if (permissions?.canManageBooks) {
      baseNavs.push({ key: 'livres', label: 'Gestion des livres', icon: BookIcon });
      baseNavs.push({ key: 'categories', label: 'Catégories', icon: CategoryIcon });
    }

    if (permissions?.canManageUsers) {
      baseNavs.push({ key: 'profils', label: 'Utilisateurs', icon: UsersIcon });
    }

    if (permissions?.canManageBooks) {
      baseNavs.push({ key: 'prets', label: 'Suivi des prêts', icon: ClipboardIcon });
    }

    if (permissions?.canManageCategories) {
      baseNavs.push({ key: 'gestion-categories', label: 'Gestion catégories', icon: SettingsIcon });
    }

    if (permissions?.canManageAdministrators) {
      baseNavs.push({ key: 'gestion-utilisateurs', label: 'Gestion admin', icon: ShieldIcon });
    }

    if (permissions?.canViewAuditLogs) {
      baseNavs.push({ key: 'audit', label: 'Journaux d\'audit', icon: ClipboardIcon });
    }

    return baseNavs;
  };

  const navItems = getNavItems();

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        const navKeys = navItems.map((_, index) => (index + 1).toString());
        const keyIndex = navKeys.indexOf(e.key);

        if (keyIndex !== -1) {
          e.preventDefault();
          setPage(navItems[keyIndex].key);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navItems]);

  // Book management functions
  const handleAddLivre = async (livre: Omit<BookImportData, 'id' | 'disponible'>) => {
    const newBook: Book = {
      ...livre,
      id: Math.max(...livres.map(l => l.id), 0) + 1,
      disponible: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setLivres(prev => [...prev, newBook]);
    logAction('CREATE', 'BOOK', newBook.id, `Livre créé: ${livre.titre}`);
  };

  const handleDeleteLivre = (id: number) => {
    const book = livres.find(l => l.id === id);
    setLivres(livres.filter(l => l.id !== id));
    if (book) {
      logAction('DELETE', 'BOOK', id, `Livre supprimé: ${book.titre}`);
    }
  };

  const handleToggleAvailability = (id: number) => {
    setLivres(livres.map(l => {
      if (l.id === id) {
        const updated = {
          ...l,
          disponible: !l.disponible,
          emprunteurId: l.disponible ? 1 : undefined,
          updatedAt: new Date().toISOString()
        };
        logAction('UPDATE', 'BOOK', id, `Statut modifié: ${updated.disponible ? 'Disponible' : 'Emprunté'}`);
        return updated;
      }
      return l;
    }));
  };

  // Category management functions
  const handleAddCategory = async (name: string, description?: string): Promise<boolean> => {
    const newCategory: Category = {
      id: Math.max(...categories.map(c => c.id), 0) + 1,
      name,
      description,
      order: categories.length,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setCategories(prev => [...prev, newCategory]);
    return true;
  };

  const handleUpdateCategory = async (id: number, name: string, description?: string): Promise<boolean> => {
    setCategories(prev => prev.map(cat =>
      cat.id === id
        ? { ...cat, name, description, updatedAt: new Date().toISOString() }
        : cat
    ));
    return true;
  };

  const handleDeleteCategory = async (id: number): Promise<boolean> => {
    const category = categories.find(c => c.id === id);
    if (!category) return false;

    // Move books from deleted category to "Non classé"
    setLivres(prev => prev.map(book =>
      book.categorie === category.name
        ? { ...book, categorie: 'Non classé', updatedAt: new Date().toISOString() }
        : book
    ));

    setCategories(prev => prev.filter(c => c.id !== id));
    return true;
  };

  const handleReorderCategories = async (reorderedCategories: Category[]): Promise<boolean> => {
    setCategories(reorderedCategories);
    return true;
  };

  const getBooksInCategory = (categoryName: string): number => {
    return livres.filter(book => book.categorie === categoryName).length;
  };

  // Bulk import function
  const handleBulkImport = async (books: BookImportData[]): Promise<ImportResult> => {
    let success = 0;
    let errors = 0;
    let duplicates = 0;
    const errorDetails: any[] = [];

    for (const bookData of books) {
      // Check for duplicates
      const isDuplicate = livres.some(existing =>
        existing.titre.toLowerCase() === bookData.titre.toLowerCase() &&
        existing.auteur.toLowerCase() === bookData.auteur.toLowerCase()
      );

      if (isDuplicate) {
        duplicates++;
        continue;
      }

      try {
        const newBook: Book = {
          ...sanitizeBookData(bookData),
          id: Math.max(...livres.map(l => l.id), 0) + success + 1,
          disponible: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setLivres(prev => [...prev, newBook]);
        success++;
      } catch (error) {
        errors++;
        errorDetails.push({ row: 0, message: 'Erreur lors de l\'ajout', data: bookData });
      }
    }

    return { success, errors, duplicates, total: books.length, errorDetails };
  };

  // Statistics for dashboard
  const stats = {
    totalBooks: livres.length,
    availableBooks: livres.filter(l => l.disponible).length,
    borrowedBooks: livres.filter(l => !l.disponible).length,
    totalUsers: UTILISATEURS.length,
  };

  const currentPageConfig = navItems.find(nav => nav.key === page);
  const categoryNames = categories.map(c => c.name);

  return (
    <div className="app">
      <div className="app-layout">
        {/* Sidebar Navigation */}
        <aside className="sidebar">
          <div className="sidebar-header">
            <h1 className="sidebar-title">BiblioTech</h1>
            <p className="sidebar-subtitle">Système de gestion moderne</p>
          </div>

          <nav>
            <ul className="nav-menu">
              {navItems.map(nav => {
                const IconComponent = nav.icon;
                return (
                  <li key={nav.key} className="nav-item">
                    <a
                      href="#"
                      className={`nav-link ${page === nav.key ? 'active' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(nav.key);
                      }}
                    >
                      <IconComponent className="nav-icon" />
                      {nav.label}
                    </a>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* User info and logout */}
          <div className="mt-auto pt-6 border-t border-neutral-700">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-semibold">
                {user?.username.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="text-sm font-medium text-neutral-100">{user?.username}</div>
                <div className="text-xs text-neutral-400">{user?.role === 'super_admin' ? 'Super Admin' : 'Administrateur'}</div>
              </div>
            </div>
            <button
              onClick={logout}
              className="flex items-center gap-2 w-full p-2 text-neutral-300 hover:text-neutral-100 hover:bg-neutral-800 rounded-lg transition-colors"
            >
              <LogoutIcon size={16} />
              Déconnexion
            </button>
          </div>
        </aside>

        {/* Main Content */}
        <main className="main-content">
          <div className="page-header">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="page-title">{currentPageConfig?.label}</h1>
                <p className="page-description">
                  {page === 'accueil' && 'Vue d\'ensemble de votre bibliothèque'}
                  {page === 'livres' && 'Gérez votre collection de livres'}
                  {page === 'categories' && 'Explorez les livres par catégorie'}
                  {page === 'profils' && 'Gérez les profils des utilisateurs'}
                  {page === 'prets' && 'Suivez les prêts en cours'}
                  {page === 'gestion-categories' && 'Gérez les catégories de livres'}
                  {page === 'gestion-utilisateurs' && 'Gérez les comptes administrateurs'}
                  {page === 'audit' && 'Consultez les journaux d\'audit'}
                </p>
              </div>
              {page === 'livres' && permissions?.canImportBooks && (
                <button
                  onClick={() => setShowBulkImport(true)}
                  className="btn btn-primary"
                >
                  <UploadIcon size={16} />
                  Import en lot
                </button>
              )}
            </div>
          </div>

          {page === 'accueil' && (
            <div>
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">{stats.totalBooks}</div>
                  <div className="stat-label">Total des livres</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">{stats.availableBooks}</div>
                  <div className="stat-label">Livres disponibles</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">{stats.borrowedBooks}</div>
                  <div className="stat-label">Livres empruntés</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">{stats.totalUsers}</div>
                  <div className="stat-label">Utilisateurs</div>
                </div>
              </div>

              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Bienvenue dans BiblioTech</h3>
                  <p className="card-subtitle">Votre système de gestion de bibliothèque moderne</p>
                </div>
                <div className="card-content">
                  <p>
                    BiblioTech vous permet de gérer efficacement votre collection de livres,
                    de suivre les prêts et d'organiser vos utilisateurs. Utilisez la navigation
                    de gauche pour accéder aux différentes fonctionnalités.
                  </p>
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Raccourcis clavier :</h4>
                    <ul className="text-sm text-neutral space-y-1">
                      {navItems.map((nav, index) => (
                        <li key={nav.key}>
                          <kbd>Ctrl/Cmd + {index + 1}</kbd> - {nav.label}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Vos permissions :</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className={permissions?.canManageBooks ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageBooks ? '✓' : '✗'} Gestion des livres
                      </div>
                      <div className={permissions?.canManageCategories ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageCategories ? '✓' : '✗'} Gestion des catégories
                      </div>
                      <div className={permissions?.canManageUsers ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageUsers ? '✓' : '✗'} Gestion des utilisateurs
                      </div>
                      <div className={permissions?.canManageAdministrators ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageAdministrators ? '✓' : '✗'} Gestion des admins
                      </div>
                      <div className={permissions?.canImportBooks ? 'text-success' : 'text-neutral'}>
                        {permissions?.canImportBooks ? '✓' : '✗'} Import en lot
                      </div>
                      <div className={permissions?.canViewAuditLogs ? 'text-success' : 'text-neutral'}>
                        {permissions?.canViewAuditLogs ? '✓' : '✗'} Journaux d'audit
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {page === 'livres' && (
            <>
              <LivreForm onAdd={handleAddLivre} categories={categoryNames} />
              <LivreList
                livres={livres}
                onDelete={handleDeleteLivre}
                onToggleAvailability={handleToggleAvailability}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
              />
            </>
          )}

          {page === 'categories' && (
            <>
              <Categories categories={categoryNames} selected={selectedCategory} onSelect={setSelectedCategory} />
              <LivreList
                livres={livres.filter((l: Book) => l.categorie === selectedCategory)}
                onDelete={handleDeleteLivre}
                onToggleAvailability={handleToggleAvailability}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
              />
            </>
          )}

          {page === 'gestion-categories' && (
            <CategoryManagement
              categories={categories}
              onAddCategory={handleAddCategory}
              onUpdateCategory={handleUpdateCategory}
              onDeleteCategory={handleDeleteCategory}
              onReorderCategories={handleReorderCategories}
              booksInCategory={getBooksInCategory}
            />
          )}

          {page === 'profils' && (
            <UtilisateurList utilisateurs={UTILISATEURS} />
          )}

          {page === 'gestion-utilisateurs' && (
            <UserManagement />
          )}

          {page === 'prets' && (
            <SuiviPrets livres={livres} utilisateurs={UTILISATEURS} />
          )}

          {page === 'audit' && (
            <AuditLogs />
          )}
        </main>
      </div>

      {/* Bulk Import Modal */}
      {showBulkImport && (
        <BulkImport
          isOpen={showBulkImport}
          onClose={() => setShowBulkImport(false)}
          onImport={handleBulkImport}
          categories={categoryNames}
        />
      )}
    </div>
  );
};

// Main App wrapper with authentication
const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppWrapper />
    </AuthProvider>
  );
};

// App wrapper to handle authentication state
const AppWrapper: React.FC = () => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Login />;
  }

  return <AppContent />;
};

export default App;
