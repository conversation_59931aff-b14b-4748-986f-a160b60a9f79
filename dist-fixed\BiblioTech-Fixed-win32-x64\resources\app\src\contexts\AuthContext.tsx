import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { User, AuthState, LoginCredentials, CreateUserData, AuditLog } from '../types/auth';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  createUser: (userData: CreateUserData) => Promise<boolean>;
  updateUser: (userId: number, updates: Partial<User>) => Promise<boolean>;
  deleteUser: (userId: number) => Promise<boolean>;
  resetPassword: (userId: number, newPassword: string) => Promise<boolean>;
  getAllUsers: () => User[];
  getAuditLogs: () => AuditLog[];
  logAction: (action: string, resource: string, resourceId?: number, details?: string) => void;
}

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true };
    case 'LOGIN_SUCCESS':
      return { user: action.payload, isAuthenticated: true, isLoading: false };
    case 'LOGIN_FAILURE':
      return { user: null, isAuthenticated: false, isLoading: false };
    case 'LOGOUT':
      return { user: null, isAuthenticated: false, isLoading: false };
    case 'UPDATE_USER':
      return { ...state, user: action.payload };
    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock data for demonstration
const MOCK_USERS: User[] = [
  {
    id: 1,
    username: 'superadmin',
    email: '<EMAIL>',
    role: 'super_admin',
    isActive: true,
    createdAt: '2025-01-01T00:00:00Z',
    lastLogin: '2025-01-15T10:30:00Z',
  },
  {
    id: 2,
    username: 'admin1',
    email: '<EMAIL>',
    role: 'administrator',
    isActive: true,
    createdAt: '2025-01-02T00:00:00Z',
    lastLogin: '2025-01-14T15:45:00Z',
  },
];

const MOCK_PASSWORDS: Record<string, string> = {
  'superadmin': 'admin123',
  'admin1': 'admin123',
};

let auditLogs: AuditLog[] = [
  {
    id: 1,
    userId: 1,
    username: 'superadmin',
    action: 'LOGIN',
    resource: 'AUTH',
    details: 'Connexion réussie',
    timestamp: '2025-01-15T10:30:00Z',
  },
];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    isLoading: false,
  });

  const [users, setUsers] = React.useState<User[]>(MOCK_USERS);
  const [passwords, setPasswords] = React.useState<Record<string, string>>(MOCK_PASSWORDS);

  // Auto-logout after 30 minutes of inactivity
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    const resetTimeout = () => {
      clearTimeout(timeoutId);
      if (state.isAuthenticated) {
        timeoutId = setTimeout(() => {
          logout();
          alert('Session expirée. Veuillez vous reconnecter.');
        }, 30 * 60 * 1000); // 30 minutes
      }
    };

    const handleActivity = () => resetTimeout();

    if (state.isAuthenticated) {
      resetTimeout();
      window.addEventListener('mousedown', handleActivity);
      window.addEventListener('keydown', handleActivity);
    }

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('mousedown', handleActivity);
      window.removeEventListener('keydown', handleActivity);
    };
  }, [state.isAuthenticated]);

  // Check for existing session on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('bibliotech_user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
      } catch (error) {
        localStorage.removeItem('bibliotech_user');
      }
    }
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    dispatch({ type: 'LOGIN_START' });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const user = users.find(u => u.username === credentials.username && u.isActive);
    const isValidPassword = passwords[credentials.username] === credentials.password;

    if (user && isValidPassword) {
      const updatedUser = { ...user, lastLogin: new Date().toISOString() };
      setUsers(prev => prev.map(u => u.id === user.id ? updatedUser : u));
      
      localStorage.setItem('bibliotech_user', JSON.stringify(updatedUser));
      dispatch({ type: 'LOGIN_SUCCESS', payload: updatedUser });
      
      logAction('LOGIN', 'AUTH', undefined, 'Connexion réussie');
      return true;
    } else {
      dispatch({ type: 'LOGIN_FAILURE' });
      return false;
    }
  };

  const logout = () => {
    if (state.user) {
      logAction('LOGOUT', 'AUTH', undefined, 'Déconnexion');
    }
    localStorage.removeItem('bibliotech_user');
    dispatch({ type: 'LOGOUT' });
  };

  const createUser = async (userData: CreateUserData): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin') {
      return false;
    }

    // Check if username or email already exists
    if (users.some(u => u.username === userData.username || u.email === userData.email)) {
      return false;
    }

    const newUser: User = {
      id: Math.max(...users.map(u => u.id), 0) + 1,
      username: userData.username,
      email: userData.email,
      role: userData.role,
      isActive: true,
      createdAt: new Date().toISOString(),
    };

    setUsers(prev => [...prev, newUser]);
    setPasswords(prev => ({ ...prev, [userData.username]: userData.password }));
    
    logAction('CREATE', 'USER', newUser.id, `Utilisateur créé: ${userData.username}`);
    return true;
  };

  const updateUser = async (userId: number, updates: Partial<User>): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin') {
      return false;
    }

    setUsers(prev => prev.map(u => 
      u.id === userId ? { ...u, ...updates, updatedAt: new Date().toISOString() } : u
    ));
    
    logAction('UPDATE', 'USER', userId, `Utilisateur modifié`);
    return true;
  };

  const deleteUser = async (userId: number): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin') {
      return false;
    }

    const user = users.find(u => u.id === userId);
    if (!user) return false;

    setUsers(prev => prev.filter(u => u.id !== userId));
    setPasswords(prev => {
      const newPasswords = { ...prev };
      delete newPasswords[user.username];
      return newPasswords;
    });
    
    logAction('DELETE', 'USER', userId, `Utilisateur supprimé: ${user.username}`);
    return true;
  };

  const resetPassword = async (userId: number, newPassword: string): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin') {
      return false;
    }

    const user = users.find(u => u.id === userId);
    if (!user) return false;

    setPasswords(prev => ({ ...prev, [user.username]: newPassword }));
    
    logAction('RESET_PASSWORD', 'USER', userId, `Mot de passe réinitialisé pour: ${user.username}`);
    return true;
  };

  const getAllUsers = (): User[] => {
    if (!state.user || state.user.role !== 'super_admin') {
      return [];
    }
    return users;
  };

  const getAuditLogs = (): AuditLog[] => {
    if (!state.user || state.user.role !== 'super_admin') {
      return [];
    }
    return auditLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const logAction = (action: string, resource: string, resourceId?: number, details?: string) => {
    if (!state.user) return;

    const log: AuditLog = {
      id: auditLogs.length + 1,
      userId: state.user.id,
      username: state.user.username,
      action,
      resource,
      resourceId,
      details: details || '',
      timestamp: new Date().toISOString(),
    };

    auditLogs.push(log);
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    createUser,
    updateUser,
    deleteUser,
    resetPassword,
    getAllUsers,
    getAuditLogs,
    logAction,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
