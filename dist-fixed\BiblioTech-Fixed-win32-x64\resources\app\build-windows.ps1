# BiblioTech Windows Build Script
# This script creates a standalone Windows executable

Write-Host "BiblioTech Windows Build Script" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Continue"

# Navigate to project directory
$ProjectPath = "C:\My Software Projects\onlinelib"
Set-Location $ProjectPath

Write-Host "Step 1: Cleaning previous builds..." -ForegroundColor Yellow

# Kill any running processes
try {
    Get-Process | Where-Object {$_.ProcessName -like "*electron*" -or $_.ProcessName -like "*BiblioTech*"} | Stop-Process -Force
    Write-Host "Stopped running processes" -ForegroundColor Green
} catch {
    Write-Host "No processes to stop" -ForegroundColor Gray
}

# Wait for file handles to release
Start-Sleep -Seconds 3

# Remove dist-electron directory with retry logic
$retryCount = 0
$maxRetries = 5
do {
    try {
        if (Test-Path "dist-electron") {
            Remove-Item -Path "dist-electron" -Recurse -Force -ErrorAction Stop
            Write-Host "Cleaned dist-electron directory" -ForegroundColor Green
            break
        }
    } catch {
        $retryCount++
        Write-Host "Retry $retryCount/$maxRetries - Waiting for file locks to release..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
} while ($retryCount -lt $maxRetries)

Write-Host "Step 2: Building React application..." -ForegroundColor Yellow

# Build the React application
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "React build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Step 3: Creating Windows executable..." -ForegroundColor Yellow

# Create Windows executable with specific configuration
npm run dist-portable
if ($LASTEXITCODE -ne 0) {
    Write-Host "Electron build failed, trying alternative method..." -ForegroundColor Yellow
    
    # Alternative build method
    npx electron-builder --win portable --config.directories.output=dist-electron
}

Write-Host "Step 4: Checking build results..." -ForegroundColor Yellow

# Check if executable was created
$exeFiles = Get-ChildItem -Path "dist-electron" -Filter "*.exe" -Recurse
if ($exeFiles.Count -gt 0) {
    Write-Host "SUCCESS: Windows executable created!" -ForegroundColor Green
    foreach ($exe in $exeFiles) {
        Write-Host "Created: $($exe.FullName)" -ForegroundColor Green
        Write-Host "Size: $([math]::Round($exe.Length / 1MB, 2)) MB" -ForegroundColor Green
    }
} else {
    Write-Host "No executable found. Checking for unpacked version..." -ForegroundColor Yellow
    
    $unpackedPath = "dist-electron\win-unpacked"
    if (Test-Path $unpackedPath) {
        Write-Host "Found unpacked version at: $unpackedPath" -ForegroundColor Green
        
        # List contents
        Get-ChildItem -Path $unpackedPath | ForEach-Object {
            Write-Host "  $($_.Name)" -ForegroundColor Gray
        }
    }
}

Write-Host "Build process completed!" -ForegroundColor Green
Write-Host "Check the dist-electron directory for output files." -ForegroundColor Cyan
