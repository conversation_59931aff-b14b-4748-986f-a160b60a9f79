import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const { login, isLoading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!credentials.username || !credentials.password) {
      setError('Veuillez remplir tous les champs');
      return;
    }

    const success = await login(credentials);
    if (!success) {
      setError('Nom d\'utilisateur ou mot de passe incorrect');
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1 className="login-title">BiblioTech</h1>
          <p className="login-subtitle">Système de gestion de bibliothèque</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username" className="form-label">
              Nom d'utilisateur
            </label>
            <input
              id="username"
              type="text"
              className="form-input"
              value={credentials.username}
              onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              placeholder="Entrez votre nom d'utilisateur"
              disabled={isLoading}
              autoComplete="username"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Mot de passe
            </label>
            <input
              id="password"
              type="password"
              className="form-input"
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              placeholder="Entrez votre mot de passe"
              disabled={isLoading}
              autoComplete="current-password"
            />
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="btn btn-primary login-button"
            disabled={isLoading}
          >
            {isLoading ? 'Connexion...' : 'Se connecter'}
          </button>
        </form>

        <div className="login-demo-info">
          <h4>Comptes de démonstration :</h4>
          <div className="demo-accounts">
            <div className="demo-account">
              <strong>Super Admin:</strong>
              <br />
              Utilisateur: <code>superadmin</code>
              <br />
              Mot de passe: <code>admin123</code>
            </div>
            <div className="demo-account">
              <strong>Administrateur:</strong>
              <br />
              Utilisateur: <code>admin1</code>
              <br />
              Mot de passe: <code>admin123</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
