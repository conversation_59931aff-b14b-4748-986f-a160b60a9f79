directories:
  output: dist-electron
  buildResources: build
appId: com.bibliotech.app
productName: BiblioTech
files:
  - filter:
      - dist/**/*
      - public/electron.cjs
      - public/assets/**/*
      - node_modules/**/*
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: public/assets/icon.ico
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: BiblioTech
  uninstallDisplayName: BiblioTech - Système de gestion de bibliothèque
portable:
  artifactName: BiblioTech-Portable-${version}.${ext}
extraResources:
  - from: public/assets
    to: assets
electronVersion: 36.4.0
