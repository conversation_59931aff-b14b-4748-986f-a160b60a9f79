// Composant pour afficher les catégories de livres
import React from 'react';
import { CategoryIcon } from './Icons';

interface Props {
  categories: string[];
  selected: string;
  onSelect: (categorie: string) => void;
}

const Categories: React.FC<Props> = ({ categories, selected, onSelect }) => (
  <div className="card mb-8">
    <div className="card-header">
      <h2 className="card-title flex items-center gap-2">
        <CategoryIcon size={24} />
        Catégories de livres
      </h2>
      <p className="card-subtitle">Filtrez votre collection par catégorie</p>
    </div>
    <div className="card-content">
      <div className="category-pills">
        {categories.map(cat => (
          <button
            key={cat}
            className={`category-pill ${selected === cat ? 'active' : ''}`}
            onClick={() => onSelect(cat)}
          >
            {cat}
          </button>
        ))}
      </div>
    </div>
  </div>
);

export default Categories;
