import React, { useState, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getPermissions } from '../types/auth';
import { SearchIcon, FilterIcon, DownloadIcon } from './Icons';
// import type { AuditLog } from '../types/auth';

const AuditLogs: React.FC = () => {
  const { user: currentUser, getAuditLogs } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterResource, setFilterResource] = useState<string>('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  const logs = getAuditLogs();
  const permissions = currentUser ? getPermissions(currentUser.role) : null;

  if (!permissions?.canViewAuditLogs) {
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <p className="text-neutral">Vous n'avez pas les permissions pour consulter les journaux d'audit.</p>
        </div>
      </div>
    );
  }

  const filteredLogs = useMemo(() => {
    let filtered = logs;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Action filter
    if (filterAction !== 'all') {
      filtered = filtered.filter(log => log.action === filterAction);
    }

    // Resource filter
    if (filterResource !== 'all') {
      filtered = filtered.filter(log => log.resource === filterResource);
    }

    // Date range filter
    if (dateRange.start) {
      filtered = filtered.filter(log => 
        new Date(log.timestamp) >= new Date(dateRange.start)
      );
    }
    if (dateRange.end) {
      filtered = filtered.filter(log => 
        new Date(log.timestamp) <= new Date(dateRange.end + 'T23:59:59')
      );
    }

    return filtered;
  }, [logs, searchTerm, filterAction, filterResource, dateRange]);

  const uniqueActions = [...new Set(logs.map(log => log.action))];
  const uniqueResources = [...new Set(logs.map(log => log.resource))];

  const getActionBadgeClass = (action: string) => {
    switch (action) {
      case 'LOGIN':
      case 'CREATE':
        return 'badge-success';
      case 'UPDATE':
      case 'REORDER':
        return 'badge-warning';
      case 'DELETE':
      case 'LOGOUT':
        return 'badge-error';
      default:
        return 'badge-neutral';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'LOGIN': return '🔐';
      case 'LOGOUT': return '🚪';
      case 'CREATE': return '➕';
      case 'UPDATE': return '✏️';
      case 'DELETE': return '🗑️';
      case 'BULK_IMPORT': return '📥';
      case 'REORDER': return '🔄';
      case 'RESET_PASSWORD': return '🔑';
      default: return '📝';
    }
  };

  const exportLogs = () => {
    const csvContent = [
      ['Date/Heure', 'Utilisateur', 'Action', 'Ressource', 'ID Ressource', 'Détails'],
      ...filteredLogs.map(log => [
        new Date(log.timestamp).toLocaleString('fr-FR'),
        log.username,
        log.action,
        log.resource,
        log.resourceId?.toString() || '',
        log.details
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Journaux d'audit</h2>
            <p className="card-subtitle">Historique des actions administratives</p>
          </div>
          <button onClick={exportLogs} className="btn btn-secondary">
            <DownloadIcon size={16} />
            Exporter CSV
          </button>
        </div>
      </div>

      <div className="card-content">
        {/* Filters */}
        <div className="mb-6">
          {/* Search */}
          <div className="search-bar mb-4">
            <SearchIcon className="search-icon" />
            <input
              type="text"
              className="search-input"
              placeholder="Rechercher dans les journaux..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filter controls */}
          <div className="filter-bar">
            <div className="filter-group">
              <FilterIcon size={16} />
              <span className="filter-label">Action :</span>
              <select 
                className="filter-select"
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
              >
                <option value="all">Toutes les actions</option>
                {uniqueActions.map(action => (
                  <option key={action} value={action}>{action}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <span className="filter-label">Ressource :</span>
              <select 
                className="filter-select"
                value={filterResource}
                onChange={(e) => setFilterResource(e.target.value)}
              >
                <option value="all">Toutes les ressources</option>
                {uniqueResources.map(resource => (
                  <option key={resource} value={resource}>{resource}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <span className="filter-label">Du :</span>
              <input
                type="date"
                className="filter-select"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>

            <div className="filter-group">
              <span className="filter-label">Au :</span>
              <input
                type="date"
                className="filter-select"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="stats-grid mb-6">
          <div className="stat-card">
            <div className="stat-value">{logs.length}</div>
            <div className="stat-label">Total entrées</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{filteredLogs.length}</div>
            <div className="stat-label">Résultats filtrés</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{uniqueActions.length}</div>
            <div className="stat-label">Types d'actions</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{[...new Set(logs.map(log => log.username))].length}</div>
            <div className="stat-label">Utilisateurs actifs</div>
          </div>
        </div>

        {/* Logs table */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>Date/Heure</th>
                <th>Utilisateur</th>
                <th>Action</th>
                <th>Ressource</th>
                <th>Détails</th>
              </tr>
            </thead>
            <tbody>
              {filteredLogs.map((log) => (
                <tr key={log.id}>
                  <td>
                    <div className="text-sm">
                      {new Date(log.timestamp).toLocaleDateString('fr-FR')}
                    </div>
                    <div className="text-xs text-neutral">
                      {new Date(log.timestamp).toLocaleTimeString('fr-FR')}
                    </div>
                  </td>
                  <td>
                    <div className="font-medium">{log.username}</div>
                    <div className="text-xs text-neutral">ID: {log.userId}</div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span>{getActionIcon(log.action)}</span>
                      <span className={`badge ${getActionBadgeClass(log.action)}`}>
                        {log.action}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="font-medium">{log.resource}</div>
                    {log.resourceId && (
                      <div className="text-xs text-neutral">ID: {log.resourceId}</div>
                    )}
                  </td>
                  <td>
                    <div className="text-sm">{log.details}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredLogs.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <p>Aucune entrée de journal trouvée.</p>
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
          <h4 className="font-semibold mb-3">Légende des actions :</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <span>🔐</span>
              <span>LOGIN - Connexion</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🚪</span>
              <span>LOGOUT - Déconnexion</span>
            </div>
            <div className="flex items-center gap-2">
              <span>➕</span>
              <span>CREATE - Création</span>
            </div>
            <div className="flex items-center gap-2">
              <span>✏️</span>
              <span>UPDATE - Modification</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🗑️</span>
              <span>DELETE - Suppression</span>
            </div>
            <div className="flex items-center gap-2">
              <span>📥</span>
              <span>BULK_IMPORT - Import en lot</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🔄</span>
              <span>REORDER - Réorganisation</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🔑</span>
              <span>RESET_PASSWORD - Réinitialisation</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditLogs;
