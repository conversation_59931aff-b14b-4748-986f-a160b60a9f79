# BiblioTech Desktop Application - Build Instructions

## Complete Step-by-Step Guide to Create Windows Executable

### Prerequisites
- Node.js (version 16 or higher)
- npm (comes with Node.js)
- Windows 10/11 (for building Windows executable)

### Step 1: Prepare the Project

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Build the React Application**
   ```bash
   npm run build
   ```

### Step 2: Test Electron in Development Mode

1. **Start Development Mode**
   ```bash
   npm run electron-dev
   ```
   This will start both the Vite dev server and Electron application.

2. **Verify Functionality**
   - Test login with demo accounts:
     - Super Admin: `superadmin` / `admin123`
     - Administrator: `admin1` / `admin123`
   - Test all features: book management, categories, users, audit logs
   - Verify bulk import functionality
   - Test keyboard shortcuts (Ctrl+1-5)

### Step 3: Create Application Icons

1. **Create ICO File**
   - Use the provided `public/assets/icon.svg`
   - Convert to ICO format (256x256) using online converter or design software
   - Save as `public/assets/icon.ico`

2. **Create PNG File**
   - Convert SVG to PNG (512x512)
   - Save as `public/assets/icon.png`

### Step 4: Build Windows Executable

#### Option A: Using Electron Builder (Recommended)

1. **Clean Previous Builds**
   ```bash
   # Close any running Electron processes first
   # Then remove the dist-electron directory
   rm -rf dist-electron  # On Linux/Mac
   # OR
   Remove-Item -Recurse -Force dist-electron  # On Windows PowerShell
   ```

2. **Build Production Version**
   ```bash
   npm run build
   ```

3. **Create Windows Installer**
   ```bash
   npm run dist-win
   ```

4. **Create Portable Executable**
   ```bash
   npm run dist-portable
   ```

#### Option B: Manual Electron Packaging

If the automated build fails, you can manually package the application:

1. **Install Electron Packager**
   ```bash
   npm install -g electron-packager
   ```

2. **Package the Application**
   ```bash
   electron-packager . BiblioTech --platform=win32 --arch=x64 --out=dist-manual --overwrite
   ```

### Step 5: Distribution Files

After successful build, you'll find these files in `dist-electron/`:

1. **Installer (NSIS)**
   - `BiblioTech Setup 1.0.0.exe` - Full installer with uninstaller
   - Creates Start Menu shortcuts and desktop shortcut
   - Allows custom installation directory

2. **Portable Executable**
   - `BiblioTech-Portable-1.0.0.exe` - Standalone executable
   - No installation required
   - Can run from USB drive or any folder

### Step 6: Testing the Built Application

1. **Test the Installer**
   - Run the setup.exe file
   - Install to a test directory
   - Verify shortcuts are created
   - Test the installed application

2. **Test the Portable Version**
   - Copy to a different folder
   - Run without installation
   - Verify all functionality works

### Step 7: Prepare for Distribution

#### For Stakeholder Demo Package

Create a folder with the following structure:
```
BiblioTech-Demo/
├── BiblioTech-Portable-1.0.0.exe
├── BiblioTech Setup 1.0.0.exe
├── README.txt
└── User-Guide.pdf
```

#### README.txt Content:
```
BiblioTech - Système de Gestion de Bibliothèque
===============================================

INSTALLATION:
1. Pour installation complète: Exécutez "BiblioTech Setup 1.0.0.exe"
2. Pour version portable: Exécutez "BiblioTech-Portable-1.0.0.exe"

COMPTES DE DÉMONSTRATION:
- Super Admin: superadmin / admin123
- Administrateur: admin1 / admin123

FONCTIONNALITÉS:
✓ Gestion des livres avec import en lot
✓ Gestion des catégories
✓ Gestion des utilisateurs
✓ Suivi des prêts
✓ Journaux d'audit
✓ Interface moderne et responsive

RACCOURCIS CLAVIER:
Ctrl+1-5: Navigation rapide
Ctrl+N: Nouveau livre
Ctrl+I: Import en lot
Ctrl+E: Exporter les données

SUPPORT:
Cette application fonctionne hors ligne et ne nécessite aucune connexion internet.
```

### Troubleshooting

#### Common Issues and Solutions:

1. **Build Fails with File Lock Error**
   - Close all Electron processes
   - Restart your terminal/command prompt
   - Delete dist-electron folder manually
   - Try building again

2. **Application Won't Start**
   - Check if Windows Defender is blocking the executable
   - Run as administrator if needed
   - Verify all dependencies are included

3. **Missing Icons**
   - Application will work with default Electron icons
   - Add custom icons later if needed

4. **Performance Issues**
   - Ensure the build is in production mode
   - Check that source maps are disabled
   - Verify the application is not running in development mode

### Advanced Configuration

#### Custom Build Settings

Edit `package.json` build configuration for:
- Different target architectures
- Custom installer options
- Code signing (for production)
- Auto-updater integration

#### Security Considerations

For production deployment:
- Enable code signing
- Configure CSP headers
- Implement proper error handling
- Add crash reporting

### Final Notes

- The application is completely offline and self-contained
- All data is stored locally (in-memory for demo)
- No external dependencies or internet connection required
- Compatible with Windows 10/11 (64-bit)
- Estimated file size: ~150-200MB (includes Chromium runtime)

This build creates a professional desktop application that maintains all the modern UI/UX and functionality of the web version while providing a native desktop experience.
