// Composant pour afficher le suivi des prêts de livres
import React, { useMemo } from 'react';
import type { Livre } from './LivreList';
import type { Utilisateur } from './UtilisateurList';
import { ClipboardIcon, CheckIcon, XIcon } from './Icons';

interface Props {
  livres: Livre[];
  utilisateurs: Utilisateur[];
}

const SuiviPrets: React.FC<Props> = ({ livres, utilisateurs }) => {
  const emprunts = livres.filter(l => !l.disponible && l.emprunteurId);

  const empruntsWithDetails = useMemo(() => {
    return emprunts.map(livre => {
      const user = utilisateurs.find(u => u.id === livre.emprunteurId);
      const dateRetour = livre.dateRetour ? new Date(livre.dateRetour) : null;
      const today = new Date();
      const isOverdue = dateRetour && dateRetour < today;
      const daysUntilDue = dateRetour ? Math.ceil((dateRetour.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : null;

      return {
        ...livre,
        user,
        isOverdue,
        daysUntilDue,
        dateRetourFormatted: dateRetour ? dateRetour.toLocaleDateString('fr-FR') : '—',
        dateEmpruntFormatted: livre.dateEmprunt ? new Date(livre.dateEmprunt).toLocaleDateString('fr-FR') : '—'
      };
    });
  }, [emprunts, utilisateurs]);

  const stats = {
    totalEmprunts: emprunts.length,
    empruntsEnRetard: empruntsWithDetails.filter(e => e.isOverdue).length,
    empruntsAEcheance: empruntsWithDetails.filter(e => e.daysUntilDue !== null && e.daysUntilDue <= 3 && e.daysUntilDue >= 0).length
  };

  return (
    <div>
      {/* Statistics Cards */}
      <div className="stats-grid mb-8">
        <div className="stat-card">
          <div className="stat-value">{stats.totalEmprunts}</div>
          <div className="stat-label">Prêts actifs</div>
        </div>
        <div className="stat-card">
          <div className="stat-value text-error">{stats.empruntsEnRetard}</div>
          <div className="stat-label">En retard</div>
        </div>
        <div className="stat-card">
          <div className="stat-value text-warning">{stats.empruntsAEcheance}</div>
          <div className="stat-label">À échéance (3j)</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title flex items-center gap-2">
            <ClipboardIcon size={24} />
            Suivi des prêts
          </h2>
          <p className="card-subtitle">Gérez les prêts de livres en cours</p>
        </div>

        <div className="card-content">
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Livre</th>
                  <th>Emprunteur</th>
                  <th>Date d'emprunt</th>
                  <th>Date de retour prévue</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {empruntsWithDetails.map(emprunt => (
                  <tr key={emprunt.id}>
                    <td>
                      <div>
                        <div className="font-medium">{emprunt.titre}</div>
                        <div className="text-sm text-neutral">{emprunt.auteur}</div>
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-3">
                        {emprunt.user && (
                          <>
                            <img
                              src={emprunt.user.photo}
                              alt={emprunt.user.nom}
                              className="w-8 h-8 rounded-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = `https://ui-avatars.com/api/?name=${encodeURIComponent(emprunt.user?.nom || '')}&background=0ea5e9&color=fff&size=32`;
                              }}
                            />
                            <span>{emprunt.user.nom}</span>
                          </>
                        )}
                        {!emprunt.user && <span className="text-neutral">Utilisateur inconnu</span>}
                      </div>
                    </td>
                    <td>{emprunt.dateEmpruntFormatted}</td>
                    <td>{emprunt.dateRetourFormatted}</td>
                    <td>
                      {emprunt.isOverdue && (
                        <span className="badge badge-error">
                          En retard
                        </span>
                      )}
                      {!emprunt.isOverdue && emprunt.daysUntilDue !== null && emprunt.daysUntilDue <= 3 && (
                        <span className="badge badge-warning">
                          Échéance proche ({emprunt.daysUntilDue}j)
                        </span>
                      )}
                      {!emprunt.isOverdue && (emprunt.daysUntilDue === null || emprunt.daysUntilDue > 3) && (
                        <span className="badge badge-success">
                          En cours
                        </span>
                      )}
                    </td>
                    <td>
                      <div className="action-buttons">
                        <button
                          className="icon-button"
                          onClick={() => {
                            // TODO: Implement return book functionality
                            console.log('Retourner le livre:', emprunt.id);
                          }}
                          title="Marquer comme retourné"
                        >
                          <CheckIcon size={16} />
                        </button>
                        <button
                          className="icon-button"
                          onClick={() => {
                            // TODO: Implement extend loan functionality
                            console.log('Prolonger le prêt:', emprunt.id);
                          }}
                          title="Prolonger le prêt"
                        >
                          <XIcon size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {emprunts.length === 0 && (
              <div className="text-center py-8 text-neutral">
                <ClipboardIcon size={48} className="mx-auto mb-4 opacity-50" />
                <p>Aucun prêt en cours.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuiviPrets;
