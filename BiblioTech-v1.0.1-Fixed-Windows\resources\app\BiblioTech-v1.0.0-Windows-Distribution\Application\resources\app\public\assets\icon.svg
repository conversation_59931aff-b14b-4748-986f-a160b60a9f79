<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="#0ea5e9" stroke="#0284c7" stroke-width="4"/>
  
  <!-- Book icon -->
  <g transform="translate(128, 128)">
    <!-- Main book -->
    <rect x="-40" y="-50" width="60" height="80" rx="4" fill="#ffffff" stroke="#e2e8f0" stroke-width="2"/>
    
    <!-- Book spine -->
    <rect x="-40" y="-50" width="8" height="80" fill="#cbd5e1"/>
    
    <!-- Book pages -->
    <rect x="-35" y="-45" width="50" height="70" fill="#f8fafc"/>
    
    <!-- Text lines -->
    <rect x="-30" y="-35" width="35" height="2" fill="#64748b"/>
    <rect x="-30" y="-25" width="40" height="2" fill="#64748b"/>
    <rect x="-30" y="-15" width="30" height="2" fill="#64748b"/>
    <rect x="-30" y="-5" width="35" height="2" fill="#64748b"/>
    <rect x="-30" y="5" width="25" height="2" fill="#64748b"/>
    
    <!-- Second book (behind) -->
    <rect x="-25" y="-45" width="55" height="75" rx="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
    <rect x="-25" y="-45" width="8" height="75" fill="#cbd5e1"/>
    
    <!-- Library symbol -->
    <circle cx="35" cy="-35" r="15" fill="#22c55e"/>
    <text x="35" y="-30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">✓</text>
  </g>
  
  <!-- App name -->
  <text x="128" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">BiblioTech</text>
</svg>
