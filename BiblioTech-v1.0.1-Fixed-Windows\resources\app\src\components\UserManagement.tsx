import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getPermissions } from '../types/auth';
import { PlusIcon, EditIcon, TrashIcon, CheckIcon, XIcon, UsersIcon } from './Icons';
import type { User, CreateUserData } from '../types/auth';

const UserManagement: React.FC = () => {
  const { user: currentUser, getAllUsers, createUser, updateUser, deleteUser, resetPassword } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<CreateUserData>({
    username: '',
    email: '',
    password: '',
    role: 'administrator'
  });
  const [editData, setEditData] = useState({ email: '', isActive: true });
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');

  const users = getAllUsers();
  const permissions = currentUser ? getPermissions(currentUser.role) : null;

  if (!permissions?.canManageAdministrators) {
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
          <p className="text-neutral">Vous n'avez pas les permissions pour gérer les utilisateurs.</p>
        </div>
      </div>
    );
  }

  const handleCreateUser = async () => {
    if (!newUser.username.trim() || !newUser.email.trim() || !newUser.password.trim()) {
      alert('Tous les champs sont requis');
      return;
    }

    if (newUser.password.length < 6) {
      alert('Le mot de passe doit contenir au moins 6 caractères');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      alert('Format d\'email invalide');
      return;
    }

    const success = await createUser(newUser);
    if (success) {
      setNewUser({ username: '', email: '', password: '', role: 'administrator' });
      setIsCreating(false);
    } else {
      alert('Erreur lors de la création de l\'utilisateur. Nom d\'utilisateur ou email déjà utilisé.');
    }
  };

  const handleUpdateUser = async (userId: number) => {
    if (!editData.email.trim()) {
      alert('L\'email est requis');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editData.email)) {
      alert('Format d\'email invalide');
      return;
    }

    const success = await updateUser(userId, {
      email: editData.email.trim(),
      isActive: editData.isActive
    });

    if (success) {
      setEditingUser(null);
      setEditData({ email: '', isActive: true });
    } else {
      alert('Erreur lors de la modification de l\'utilisateur');
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (user.id === currentUser?.id) {
      alert('Vous ne pouvez pas supprimer votre propre compte');
      return;
    }

    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir supprimer l'utilisateur "${user.username}" ? Cette action est irréversible.`
    );

    if (confirmed) {
      const success = await deleteUser(user.id);
      if (!success) {
        alert('Erreur lors de la suppression de l\'utilisateur');
      }
    }
  };

  const handleResetPassword = async () => {
    if (!resetPasswordUser || !newPassword.trim()) {
      alert('Veuillez saisir un nouveau mot de passe');
      return;
    }

    if (newPassword.length < 6) {
      alert('Le mot de passe doit contenir au moins 6 caractères');
      return;
    }

    const success = await resetPassword(resetPasswordUser.id, newPassword);
    if (success) {
      setResetPasswordUser(null);
      setNewPassword('');
      alert('Mot de passe réinitialisé avec succès');
    } else {
      alert('Erreur lors de la réinitialisation du mot de passe');
    }
  };

  const startEdit = (user: User) => {
    setEditingUser(user);
    setEditData({ email: user.email, isActive: user.isActive });
  };

  const cancelEdit = () => {
    setEditingUser(null);
    setEditData({ email: '', isActive: true });
  };

  return (
    <div>
      <div className="card">
        <div className="card-header">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="card-title flex items-center gap-2">
                <UsersIcon size={24} />
                Gestion des utilisateurs
              </h2>
              <p className="card-subtitle">Gérez les comptes administrateurs</p>
            </div>
            <button 
              onClick={() => setIsCreating(true)} 
              className="btn btn-primary"
              disabled={isCreating}
            >
              <PlusIcon size={16} />
              Nouvel administrateur
            </button>
          </div>
        </div>

        <div className="card-content">
          {/* Create new user form */}
          {isCreating && (
            <div className="card mb-6" style={{ border: '2px solid var(--color-primary-200)' }}>
              <div className="card-header">
                <h3 className="card-title">Créer un nouvel administrateur</h3>
              </div>
              <div className="card-content">
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Nom d'utilisateur *</label>
                    <input
                      type="text"
                      className="form-input"
                      value={newUser.username}
                      onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="nom_utilisateur"
                      autoFocus
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Email *</label>
                    <input
                      type="email"
                      className="form-input"
                      value={newUser.email}
                      onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div className="form-group">
                  <label className="form-label">Mot de passe *</label>
                  <input
                    type="password"
                    className="form-input"
                    value={newUser.password}
                    onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Minimum 6 caractères"
                  />
                </div>
                <div className="flex gap-3 justify-end mt-4">
                  <button 
                    onClick={() => {
                      setIsCreating(false);
                      setNewUser({ username: '', email: '', password: '', role: 'administrator' });
                    }}
                    className="btn btn-secondary"
                  >
                    Annuler
                  </button>
                  <button 
                    onClick={handleCreateUser}
                    className="btn btn-primary"
                    disabled={!newUser.username.trim() || !newUser.email.trim() || !newUser.password.trim()}
                  >
                    <CheckIcon size={16} />
                    Créer
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Users table */}
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Utilisateur</th>
                  <th>Email</th>
                  <th>Rôle</th>
                  <th>Statut</th>
                  <th>Dernière connexion</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <div className="font-medium">{user.username}</div>
                      <div className="text-sm text-neutral">ID: {user.id}</div>
                    </td>
                    <td>
                      {editingUser?.id === user.id ? (
                        <input
                          type="email"
                          className="form-input"
                          value={editData.email}
                          onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                        />
                      ) : (
                        user.email
                      )}
                    </td>
                    <td>
                      <span className={`badge ${user.role === 'super_admin' ? 'badge-error' : 'badge-neutral'}`}>
                        {user.role === 'super_admin' ? 'Super Admin' : 'Administrateur'}
                      </span>
                    </td>
                    <td>
                      {editingUser?.id === user.id ? (
                        <select
                          className="form-select"
                          value={editData.isActive ? 'active' : 'inactive'}
                          onChange={(e) => setEditData(prev => ({ ...prev, isActive: e.target.value === 'active' }))}
                        >
                          <option value="active">Actif</option>
                          <option value="inactive">Inactif</option>
                        </select>
                      ) : (
                        <span className={`badge ${user.isActive ? 'badge-success' : 'badge-warning'}`}>
                          {user.isActive ? 'Actif' : 'Inactif'}
                        </span>
                      )}
                    </td>
                    <td>
                      {user.lastLogin 
                        ? new Date(user.lastLogin).toLocaleDateString('fr-FR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        : 'Jamais'
                      }
                    </td>
                    <td>
                      <div className="action-buttons">
                        {editingUser?.id === user.id ? (
                          <>
                            <button
                              onClick={() => handleUpdateUser(user.id)}
                              className="icon-button"
                              title="Sauvegarder"
                            >
                              <CheckIcon size={16} />
                            </button>
                            <button
                              onClick={cancelEdit}
                              className="icon-button"
                              title="Annuler"
                            >
                              <XIcon size={16} />
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => startEdit(user)}
                              className="icon-button"
                              title="Modifier"
                              disabled={user.id === currentUser?.id}
                            >
                              <EditIcon size={16} />
                            </button>
                            <button
                              onClick={() => setResetPasswordUser(user)}
                              className="icon-button"
                              title="Réinitialiser le mot de passe"
                            >
                              🔑
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user)}
                              className="icon-button danger"
                              title="Supprimer"
                              disabled={user.id === currentUser?.id || user.role === 'super_admin'}
                            >
                              <TrashIcon size={16} />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {users.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
              <p>Aucun utilisateur trouvé.</p>
            </div>
          )}
        </div>
      </div>

      {/* Reset Password Modal */}
      {resetPasswordUser && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2 className="modal-title">Réinitialiser le mot de passe</h2>
              <p className="modal-subtitle">
                Utilisateur: {resetPasswordUser.username}
              </p>
            </div>
            <div className="modal-content">
              <div className="form-group">
                <label className="form-label">Nouveau mot de passe *</label>
                <input
                  type="password"
                  className="form-input"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Minimum 6 caractères"
                  autoFocus
                />
              </div>
            </div>
            <div className="modal-footer">
              <button 
                onClick={() => {
                  setResetPasswordUser(null);
                  setNewPassword('');
                }}
                className="btn btn-secondary"
              >
                Annuler
              </button>
              <button 
                onClick={handleResetPassword}
                className="btn btn-primary"
                disabled={!newPassword.trim() || newPassword.length < 6}
              >
                Réinitialiser
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
