@echo off
echo ========================================
echo BiblioTech Production Build Script
echo ========================================
echo.

echo Step 1: Stopping any running BiblioTech processes...
taskkill /F /IM BiblioTech.exe 2>nul
taskkill /F /IM electron.exe 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Cleaning build directories...
if exist "dist-electron" (
    echo Removing dist-electron directory...
    rmdir /S /Q "dist-electron" 2>nul
    timeout /t 1 /nobreak >nul
)

if exist "dist" (
    echo Removing dist directory...
    rmdir /S /Q "dist" 2>nul
)

echo Step 3: Building React application...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Step 4: Creating portable executable...
call npx electron-builder --win portable --x64
if %ERRORLEVEL% NEQ 0 (
    echo Electron build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: dist-electron\
dir /B dist-electron\*.exe 2>nul
echo.
echo Press any key to exit...
pause >nul
