# BiblioTech Manual Build Script using Electron Packager
# This creates a standalone Windows executable without file locking issues

Write-Host "BiblioTech Manual Build Script" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Continue"

# Navigate to project directory
$ProjectPath = "C:\My Software Projects\onlinelib"
Set-Location $ProjectPath

Write-Host "Step 1: Cleaning up..." -ForegroundColor Yellow

# Kill any running processes
try {
    Get-Process | Where-Object {$_.ProcessName -like "*electron*" -or $_.ProcessName -like "*BiblioTech*"} | Stop-Process -Force
    Write-Host "Stopped running processes" -ForegroundColor Green
} catch {
    Write-Host "No processes to stop" -ForegroundColor Gray
}

# Clean up previous builds
if (Test-Path "dist-manual") {
    Remove-Item -Path "dist-manual" -Recurse -Force
    Write-Host "Cleaned dist-manual directory" -ForegroundColor Green
}

Write-Host "Step 2: Building React application..." -ForegroundColor Yellow

# Build the React application
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "React build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Step 3: Packaging with Electron Packager..." -ForegroundColor Yellow

# Use electron-packager to create the executable
electron-packager . BiblioTech `
    --platform=win32 `
    --arch=x64 `
    --out=dist-manual `
    --overwrite `
    --app-version=1.0.0 `
    --build-version=1.0.0 `
    --app-copyright="BiblioTech Demo 2025" `
    --win32metadata.CompanyName="BiblioTech" `
    --win32metadata.ProductName="BiblioTech Library Management" `
    --win32metadata.FileDescription="Modern Library Management System" `
    --executable-name="BiblioTech" `
    --ignore="node_modules/((?!electron).)*" `
    --ignore="src" `
    --ignore="public/(?!electron\.cjs|assets)" `
    --ignore="\.git" `
    --ignore="\.vscode" `
    --ignore="build-.*\.ps1" `
    --ignore=".*\.md$"

if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: Electron packaging completed!" -ForegroundColor Green
    
    # Check the output
    $outputDir = "dist-manual\BiblioTech-win32-x64"
    if (Test-Path $outputDir) {
        Write-Host "Application packaged to: $outputDir" -ForegroundColor Green
        
        # List main files
        Get-ChildItem -Path $outputDir | ForEach-Object {
            if ($_.Name -like "*.exe" -or $_.Name -like "*.dll" -or $_.Name -eq "resources") {
                Write-Host "  $($_.Name)" -ForegroundColor Cyan
            }
        }
        
        # Check executable size
        $exePath = Join-Path $outputDir "BiblioTech.exe"
        if (Test-Path $exePath) {
            $size = (Get-Item $exePath).Length
            Write-Host "Executable size: $([math]::Round($size / 1MB, 2)) MB" -ForegroundColor Green
        }
        
        # Calculate total package size
        $totalSize = (Get-ChildItem -Path $outputDir -Recurse | Measure-Object -Property Length -Sum).Sum
        Write-Host "Total package size: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Green
        
    } else {
        Write-Host "Output directory not found!" -ForegroundColor Red
    }
} else {
    Write-Host "Electron packaging failed!" -ForegroundColor Red
}

Write-Host "Step 4: Creating portable package..." -ForegroundColor Yellow

# Create a portable package
$portableDir = "BiblioTech-Portable"
if (Test-Path $portableDir) {
    Remove-Item -Path $portableDir -Recurse -Force
}

$sourceDir = "dist-manual\BiblioTech-win32-x64"
if (Test-Path $sourceDir) {
    # Copy the packaged application
    Copy-Item -Path $sourceDir -Destination $portableDir -Recurse
    
    # Create a launcher script
    $launcherContent = @"
@echo off
echo Starting BiblioTech Library Management System...
echo.
echo Demo Accounts:
echo Super Admin: superadmin / admin123
echo Administrator: admin1 / admin123
echo.
echo Press any key to start the application...
pause >nul
start "" "BiblioTech.exe"
"@
    
    Set-Content -Path "$portableDir\Start-BiblioTech.bat" -Value $launcherContent
    
    # Create README
    $readmeContent = @"
BiblioTech - Portable Library Management System
==============================================

QUICK START:
1. Double-click "Start-BiblioTech.bat" to launch the application
2. Or directly run "BiblioTech.exe"

DEMO ACCOUNTS:
- Super Admin: superadmin / admin123
- Administrator: admin1 / admin123

FEATURES:
✓ Complete offline library management
✓ Book management with bulk import
✓ Category management
✓ User management and role-based access
✓ Loan tracking and audit logs
✓ Modern desktop interface

SYSTEM REQUIREMENTS:
- Windows 10 (1903+) or Windows 11
- 64-bit architecture
- 4 GB RAM recommended
- No additional software required

This is a completely portable application that requires no installation.
All components are included in this folder.

© 2025 BiblioTech Demo
"@
    
    Set-Content -Path "$portableDir\README.txt" -Value $readmeContent
    
    Write-Host "Portable package created: $portableDir" -ForegroundColor Green
    
    # Calculate portable package size
    $portableSize = (Get-ChildItem -Path $portableDir -Recurse | Measure-Object -Property Length -Sum).Sum
    Write-Host "Portable package size: $([math]::Round($portableSize / 1MB, 2)) MB" -ForegroundColor Green
}

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "You can now distribute the '$portableDir' folder" -ForegroundColor Cyan
